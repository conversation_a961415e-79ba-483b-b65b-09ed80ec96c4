<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>قراء القرآن الكريم - استمع إلى التلاوات الجميلة</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Cairo:wght@400;600;700&display=swap"
        rel="stylesheet">
    <style>
        :root {
            --primary-green: #059669;
            --primary-blue: #0284c7;
            --accent-gold: #d97706;
            --light-green: #ecfdf5;
            --light-blue: #f0f9ff;
            --dark-text: #1f2937;
            --medium-text: #4b5563;
            --light-text: #6b7280;
            --border-color: #e5e7eb;
            --shadow-light: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            --shadow-medium: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-large: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, var(--light-green) 0%, var(--light-blue) 100%);
            min-height: 100vh;
            line-height: 1.6;
        }

        .arabic-text {
            font-family: 'Cairo', sans-serif;
            font-weight: 600;
            line-height: 1.4;
        }

        /* Modern gradient backgrounds */
        .gradient-primary {
            background: linear-gradient(135deg, var(--primary-green) 0%, var(--primary-blue) 100%);
        }

        .gradient-accent {
            background: linear-gradient(135deg, var(--accent-gold) 0%, var(--primary-green) 100%);
        }

        .gradient-light {
            background: linear-gradient(135deg, var(--light-green) 0%, var(--light-blue) 100%);
        }

        /* Modern card styling */
        .modern-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: var(--shadow-medium);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .modern-card:hover {
            transform: translateY(-8px);
            box-shadow: var(--shadow-xl);
            border-color: rgba(5, 150, 105, 0.2);
        }

        /* Enhanced search input */
        .search-input {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border: 2px solid transparent;
            box-shadow: var(--shadow-light);
            transition: all 0.3s ease;
        }

        .search-input:focus {
            background: rgba(255, 255, 255, 1);
            border-color: var(--primary-green);
            box-shadow: 0 0 0 3px rgba(5, 150, 105, 0.1), var(--shadow-medium);
            transform: translateY(-2px);
        }

        /* Modern button styling */
        .btn-modern {
            background: linear-gradient(135deg, var(--primary-green) 0%, var(--primary-blue) 100%);
            border: none;
            box-shadow: var(--shadow-light);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            color: white;
        }

        .btn-modern::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, transparent 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .btn-modern:hover::before {
            opacity: 1;
        }

        .btn-modern:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-large);
        }

        /* Custom animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(30px);
            }

            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes pulse {

            0%,
            100% {
                opacity: 1;
            }

            50% {
                opacity: 0.8;
            }
        }

        .animate-fade-in-up {
            animation: fadeInUp 0.8s cubic-bezier(0.4, 0, 0.2, 1) forwards;
            opacity: 0;
            animation-fill-mode: forwards;
        }

        /* Ensure cards are visible by default if animation fails */
        .modern-card {
            opacity: 1;
        }

        .animate-fade-in-up.modern-card {
            opacity: 0;
        }

        .animate-slide-in-right {
            animation: slideInRight 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
            opacity: 0;
        }

        /* Enhanced loading spinner */
        .modern-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid var(--light-green);
            border-top: 3px solid var(--primary-green);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        /* Modern section dividers */
        .section-divider {
            height: 1px;
            background: linear-gradient(90deg, transparent 0%, var(--border-color) 50%, transparent 100%);
            margin: 3rem 0;
        }

        /* Enhanced typography */
        .heading-primary {
            font-size: clamp(2.5rem, 5vw, 4rem);
            font-weight: 700;
            background: linear-gradient(135deg, var(--primary-green) 0%, var(--primary-blue) 50%, var(--accent-gold) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            line-height: 1.2;
        }

        .text-gradient {
            background: linear-gradient(135deg, var(--primary-green) 0%, var(--primary-blue) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* Alpine.js cloak */
        [x-cloak] {
            display: none !important;
        }

        /* Responsive improvements */
        @media (max-width: 768px) {
            .modern-card {
                margin-bottom: 1rem;
            }

            .search-input {
                font-size: 16px;
                /* Prevent zoom on iOS */
            }
        }
    </style>
</head>

<body>
    <div x-data="recitersApp()" class="min-h-screen">
        <!-- Modern Header -->
        <header class="relative overflow-hidden">
            <!-- Background Pattern -->
            <div class="absolute inset-0 gradient-light opacity-90"></div>
            <div class="absolute inset-0"
                style="background-image: radial-gradient(circle at 25% 25%, rgba(5, 150, 105, 0.1) 0%, transparent 50%), radial-gradient(circle at 75% 75%, rgba(2, 132, 199, 0.1) 0%, transparent 50%);">
            </div>

            <div class="relative container mx-auto px-6 py-16 text-center">
                <!-- Main Title -->
                <div class="mb-8 animate-fade-in-up">
                    <div
                        class="inline-flex items-center justify-center w-20 h-20 rounded-full gradient-primary mb-6 shadow-lg">
                        <i class="fas fa-mosque text-white text-2xl"></i>
                    </div>
                    <h1 class="heading-primary arabic-text mb-4">
                        قراء القرآن الكريم
                    </h1>
                    <div class="w-24 h-1 gradient-accent mx-auto rounded-full mb-6"></div>
                </div>

                <!-- Subtitle -->
                <p class="text-xl text-gray-700 max-w-3xl mx-auto mb-12 leading-relaxed animate-slide-in-right"
                    style="animation-delay: 0.2s;">
                    استمع إلى التلاوات الجميلة للقرآن الكريم من قراء مشهورين حول العالم
                </p>

                <!-- Modern Search Section -->
                <div class="max-w-lg mx-auto animate-fade-in-up" style="animation-delay: 0.4s;">
                    <div class="relative group">
                        <div class="absolute inset-y-0 right-0 pr-4 flex items-center pointer-events-none">
                            <i
                                class="fas fa-search text-gray-400 group-focus-within:text-green-600 transition-colors duration-300"></i>
                        </div>
                        <input type="text" x-model="searchQuery" @input.debounce.300ms="filterReciters()"
                            placeholder="ابحث عن قارئ... (مثال: مشاري العفاسي، ماهر المعيقلي)"
                            class="search-input w-full pl-12 pr-12 py-4 rounded-2xl text-right arabic-text text-lg focus:outline-none"
                            dir="rtl">
                        <button x-show="searchQuery.length > 0" @click="clearSearch()"
                            x-transition:enter="transition ease-out duration-200"
                            x-transition:enter-start="opacity-0 scale-95" x-transition:enter-end="opacity-100 scale-100"
                            x-transition:leave="transition ease-in duration-150"
                            x-transition:leave-start="opacity-100 scale-100" x-transition:leave-end="opacity-0 scale-95"
                            class="absolute inset-y-0 left-0 pl-4 flex items-center cursor-pointer text-gray-400 hover:text-red-500 transition-colors duration-200">
                            <i class="fas fa-times-circle text-lg"></i>
                        </button>
                    </div>

                    <!-- Enhanced Search Results Count -->
                    <div x-show="searchQuery.length > 0" x-transition:enter="transition ease-out duration-300"
                        x-transition:enter-start="opacity-0 transform translate-y-2"
                        x-transition:enter-end="opacity-100 transform translate-y-0" class="mt-4 text-center">
                        <div
                            class="inline-flex items-center px-4 py-2 bg-white/80 backdrop-blur-sm rounded-full shadow-sm border border-gray-200">
                            <i class="fas fa-filter text-green-600 ml-2"></i>
                            <span class="text-sm font-medium text-gray-700">
                                <span x-text="filteredReciters.length" class="text-gradient font-bold"></span>
                                قارئ من أصل
                                <span x-text="reciters.length" class="font-bold"></span>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <!-- Main Content Area -->
        <main class="container mx-auto px-6 pb-16">
            <!-- Modern Loading State -->
            <div x-show="loading" class="flex flex-col justify-center items-center py-24">
                <div class="modern-spinner mb-6"></div>
                <p class="text-lg font-medium text-gray-600 arabic-text">جاري تحميل القراء...</p>
                <p class="text-sm text-gray-500 mt-2">يرجى الانتظار قليلاً</p>
            </div>

            <!-- Enhanced No Results Message -->
            <div x-show="!loading && filteredReciters.length === 0 && searchQuery.length > 0"
                class="text-center py-20 animate-fade-in-up">
                <div class="max-w-lg mx-auto modern-card rounded-3xl p-12">
                    <div class="w-24 h-24 mx-auto mb-6 rounded-full gradient-light flex items-center justify-center">
                        <i class="fas fa-search text-gray-400 text-3xl"></i>
                    </div>
                    <h3 class="text-3xl font-bold text-gray-700 mb-4 arabic-text">لا توجد نتائج</h3>
                    <p class="text-gray-600 mb-8 text-lg leading-relaxed">
                        لم نجد أي قارئ يطابق بحثك عن
                        <span x-text="searchQuery" class="font-bold text-gradient"></span>
                    </p>
                    <button @click="clearSearch()"
                        class="btn-modern text-white py-3 px-8 rounded-2xl font-semibold text-lg">
                        <i class="fas fa-refresh ml-2"></i>
                        مسح البحث والعودة
                    </button>
                </div>
            </div>



            <!-- Modern Reciters Grid -->
            <div x-show="!loading && filteredReciters.length > 0" x-cloak
                class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
                <template x-for="(reciter, index) in filteredReciters" :key="reciter.id">
                    <div class="modern-card rounded-3xl cursor-pointer group" :class="'animate-fade-in-up'"
                        :style="`animation-delay: ${index * 0.1}s`" @click="goToReciter(reciter)">
                        <div class="p-8 text-center">
                            <!-- Modern Reciter Avatar -->
                            <div class="relative mb-6">
                                <div
                                    class="w-28 h-28 mx-auto rounded-full gradient-primary flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                                    <i class="fas fa-user text-white text-3xl" x-show="!reciter.image"></i>
                                    <img x-show="reciter.image" :src="reciter.image" :alt="reciter.name"
                                        class="w-full h-full rounded-full object-cover">
                                </div>
                                <!-- Decorative ring -->
                                <div
                                    class="absolute inset-0 w-28 h-28 mx-auto rounded-full border-2 border-green-200 opacity-0 group-hover:opacity-100 group-hover:scale-125 transition-all duration-300">
                                </div>
                            </div>

                            <!-- Enhanced Reciter Info -->
                            <div class="space-y-3 mb-6">
                                <h3 class="text-2xl font-bold text-gray-800 arabic-text leading-tight group-hover:text-gradient transition-all duration-300"
                                    x-text="reciter.arabicName"></h3>
                                <p class="text-gray-600 font-medium" x-text="reciter.name"></p>

                                <!-- Modern Stats Badge -->
                                <div
                                    class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-green-50 to-blue-50 rounded-full border border-green-100">
                                    <i class="fas fa-book-quran text-green-600 ml-2"></i>
                                    <span class="text-sm font-semibold text-gray-700">١١٤ سورة</span>
                                </div>
                            </div>

                            <!-- Modern Action Button -->
                            <button
                                class="btn-modern w-full text-white py-3 px-6 rounded-2xl font-semibold text-lg group-hover:scale-105 transition-transform duration-200">
                                <i class="fas fa-play ml-2"></i>
                                استمع للتلاوات
                            </button>
                        </div>
                    </div>
                </template>
            </div>

        </main>

        <!-- Modern Footer -->
        <footer class="relative mt-20">
            <div class="section-divider"></div>
            <div class="container mx-auto px-6 py-12 text-center">
                <div class="max-w-2xl mx-auto">
                    <div class="mb-6">
                        <div
                            class="inline-flex items-center justify-center w-16 h-16 rounded-full gradient-primary mb-4 shadow-lg">
                            <i class="fas fa-mosque text-white text-xl"></i>
                        </div>
                    </div>
                    <p class="text-lg font-medium text-gray-700 arabic-text mb-4">
                        جميع الحقوق محفوظة لموقع قراء القرآن الكريم
                    </p>
                    <p class="text-sm text-gray-500">
                        تم التطوير بحب للمجتمع الإسلامي ❤️
                    </p>
                </div>
            </div>
        </footer>
    </div>

    <script>
        function recitersApp() {
            return {
                loading: true,
                reciters: [],
                filteredReciters: [],
                searchQuery: '',

                init() {
                    this.loadReciters();
                },

                async loadReciters() {
                    try {
                        // Load reciters from mp3quran.net API
                        const response = await fetch('https://mp3quran.net/api/v3/reciters?language=ar');

                        if (!response.ok) {
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }

                        const data = await response.json();

                        if (data.reciters && data.reciters.length > 0) {
                            // Select popular reciters and transform data
                            const popularReciterIds = [123, 102, 60, 86, 89, 92, 118, 112, 111, 109, 106, 13, 8, 6, 76, 87, 88, 96, 97, 74, 72, 71, 69, 67, 66, 62, 61, 59, 58, 57];

                            this.reciters = data.reciters
                                .filter(reciter => popularReciterIds.includes(reciter.id))
                                .slice(0, 30)
                                .map(reciter => ({
                                    id: reciter.id,
                                    name: this.getEnglishName(reciter.name),
                                    arabicName: reciter.name,
                                    country: this.getCountryFromName(reciter.name),
                                    image: null,
                                    moshaf: reciter.moshaf
                                }));
                        } else {
                            // Fallback to static data if API fails
                            this.loadFallbackReciters();
                        }

                        // Initialize filtered reciters with all reciters
                        this.filteredReciters = [...this.reciters];
                        console.log('Reciters loaded:', this.reciters.length);
                        this.loading = false;
                    } catch (error) {
                        console.error('Error loading reciters:', error);
                        // Load fallback data on error
                        this.loadFallbackReciters();
                        this.filteredReciters = [...this.reciters];
                        this.loading = false;
                    }
                },

                loadFallbackReciters() {
                    // Fallback reciters data
                    this.reciters = [
                        { id: 123, name: 'Mishary Al-Afasy', arabicName: 'مشاري العفاسي', country: 'الكويت', image: null },
                        { id: 102, name: 'Maher Al-Muaiqly', arabicName: 'ماهر المعيقلي', country: 'السعودية', image: null },
                        { id: 60, name: 'Abdullah Basfar', arabicName: 'عبد الله بصفر', country: 'السعودية', image: null },
                        { id: 86, name: 'Nasser Al-Qatami', arabicName: 'ناصر القطامي', country: 'السعودية', image: null },
                        { id: 89, name: 'Hani Ar-Rifai', arabicName: 'هاني الرفاعي', country: 'السعودية', image: null },
                        { id: 92, name: 'Yasser Al-Dosari', arabicName: 'ياسر الدوسري', country: 'السعودية', image: null },
                        { id: 118, name: 'Mahmoud Khalil Al-Husary', arabicName: 'محمود خليل الحصري', country: 'مصر', image: null },
                        { id: 112, name: 'Mohamed Siddiq El-Minshawi', arabicName: 'محمد صديق المنشاوي', country: 'مصر', image: null },
                        { id: 111, name: 'Muhammad Jibreel', arabicName: 'محمد جبريل', country: 'السعودية', image: null },
                        { id: 109, name: 'Muhammad Ayyub', arabicName: 'محمد أيوب', country: 'السعودية', image: null }
                    ];
                },

                getEnglishName(arabicName) {
                    const nameMap = {
                        'مشاري العفاسي': 'Mishary Al-Afasy',
                        'ماهر المعيقلي': 'Maher Al-Muaiqly',
                        'عبد الله بصفر': 'Abdullah Basfar',
                        'ناصر القطامي': 'Nasser Al-Qatami',
                        'هاني الرفاعي': 'Hani Ar-Rifai',
                        'ياسر الدوسري': 'Yasser Al-Dosari',
                        'محمود خليل الحصري': 'Mahmoud Khalil Al-Husary',
                        'محمد صديق المنشاوي': 'Mohamed Siddiq El-Minshawi',
                        'محمد جبريل': 'Muhammad Jibreel',
                        'محمد أيوب': 'Muhammad Ayyub',
                        'محمد الطبلاوي': 'Muhammad At-Tablawi',
                        'الزين محمد أحمد': 'Alzain Mohammad Ahmad',
                        'أحمد صابر': 'Ahmad Saber',
                        'أحمد الحواشي': 'Ahmad Al-Hawashi',
                        'علي جابر': 'Ali Jaber',
                        'نبيل الرفاعي': 'Nabil Ar-Rifai',
                        'نعمة الحسان': 'Ni\'mah Al-Hassan',
                        'يحيى حوا': 'Yahya Hawwa',
                        'يوسف الشويعي': 'Yusuf Ash-Shuway\'i',
                        'علي بن عبدالرحمن الحذيفي': 'Ali Al-Hudhaifi',
                        'عبدالولي الأركاني': 'Abdul Wali Al-Arkani',
                        'عبدالودود حنيف': 'Abdul Wadud Hanif',
                        'عبدالمحسن العبيكان': 'Abdul Mohsen Al-Obaykan',
                        'عبدالمحسن القاسم': 'Abdul Mohsen Al-Qasim',
                        'عبدالمحسن الحارثي': 'Abdul Mohsen Al-Harthi',
                        'عبدالله عواد الجهني': 'Abdullah Awad Al-Juhani',
                        'عبدالله خياط': 'Abdullah Khayat',
                        'عبدالله المطرود': 'Abdullah Al-Matrood',
                        'عبدالله البعيجان': 'Abdullah Al-Buaijan',
                        'عبدالله غيلان': 'Abdullah Ghaylan'
                    };

                    // Try to find exact match first
                    for (const [arabic, english] of Object.entries(nameMap)) {
                        if (arabicName.includes(arabic)) {
                            return english;
                        }
                    }

                    // Return transliterated version if no match found
                    return arabicName;
                },

                getCountryFromName(arabicName) {
                    // Most reciters are from Saudi Arabia, with some from Egypt, Kuwait, etc.
                    if (arabicName.includes('الحصري') || arabicName.includes('المنشاوي') || arabicName.includes('الطبلاوي')) {
                        return 'مصر';
                    } else if (arabicName.includes('العفاسي')) {
                        return 'الكويت';
                    } else {
                        return 'السعودية';
                    }
                },

                goToReciter(reciter) {
                    // Navigate to reciter page with URL parameters
                    const params = new URLSearchParams({
                        id: reciter.id,
                        name: reciter.name,
                        arabicName: reciter.arabicName,
                        country: reciter.country
                    });
                    window.location.href = `reciter.html?${params.toString()}`;
                },

                filterReciters() {
                    if (!this.searchQuery.trim()) {
                        this.filteredReciters = [...this.reciters];
                        return;
                    }

                    const query = this.normalizeArabicText(this.searchQuery.toLowerCase());

                    this.filteredReciters = this.reciters.filter(reciter => {
                        const arabicName = this.normalizeArabicText(reciter.arabicName.toLowerCase());
                        const englishName = reciter.name.toLowerCase();

                        return arabicName.includes(query) ||
                            englishName.includes(query);
                    });
                },

                normalizeArabicText(text) {
                    // Normalize Arabic text by removing diacritics and standardizing characters
                    return text
                        .replace(/[ًٌٍَُِّْ]/g, '') // Remove diacritics
                        .replace(/[أإآ]/g, 'ا')      // Normalize alif
                        .replace(/[ةه]/g, 'ه')       // Normalize teh marbuta
                        .replace(/[ىي]/g, 'ي')       // Normalize yeh
                        .trim();
                },

                clearSearch() {
                    this.searchQuery = '';
                    this.filteredReciters = [...this.reciters];
                }
            }
        }
    </script>
</body>

</html>