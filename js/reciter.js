/**
 * Islamic Quran Reciters - Reciter Page JavaScript
 * Handles surah listing, audio playback, search functionality, and audio controls
 */

function reciterApp() {
    return {
        loading: true,
        loadingAudio: null,
        reciterName: '',
        reciterArabicName: '',
        reciterCountry: '',
        reciterId: '',
        surahs: [],
        filteredSurahs: [],
        searchQuery: '',
        currentAudio: {
            playing: false,
            isPlaying: false,
            surahName: '',
            surahArabicName: '',
            surahNumber: 0,
            progress: 0,
            currentTime: 0,
            duration: 0,
            volume: 75,
            muted: false,
            currentSurahIndex: -1
        },
        downloadingAudio: null,
        showingInfo: false,
        selectedSurah: null,

        init() {
            this.getReciterInfo();
            this.loadSurahs();

            // Initialize slider styles
            this.$nextTick(() => {
                this.updateVolumeSliderStyle();
                this.updateProgressSliderStyle();
            });
        },

        getReciterInfo() {
            const urlParams = new URLSearchParams(window.location.search);
            this.reciterId = urlParams.get('id') || '123'; // Default to <PERSON><PERSON>ry Al-Afasy
            this.reciterName = urlParams.get('name') || 'Unknown Reciter';
            this.reciterArabicName = urlParams.get('arabicName') || 'قارئ غير معروف';
            this.reciterCountry = urlParams.get('country') || '';
        },

        async loadSurahs() {
            try {
                // Load surah information from mp3quran.net API
                const response = await fetch('https://mp3quran.net/api/v3/suwar?language=ar');

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();

                if (data.suwar && data.suwar.length > 0) {
                    // Transform the data to match our expected format
                    this.surahs = data.suwar.map(surah => ({
                        number: surah.id,
                        name: surah.name,
                        englishName: this.getEnglishSurahName(surah.id),
                        numberOfAyahs: this.getAyahCount(surah.id),
                        revelationType: surah.makkia === 1 ? 'Meccan' : 'Medinan'
                    }));
                } else {
                    // Fallback to static data if API fails
                    this.loadFallbackSurahs();
                }

                // Initialize filtered surahs with all surahs
                this.filteredSurahs = [...this.surahs];
                console.log('Surahs loaded:', this.surahs.length);
                this.loading = false;
            } catch (error) {
                console.error('Error loading surahs:', error);
                // Load fallback data on error
                this.loadFallbackSurahs();
                this.filteredSurahs = [...this.surahs];
                this.loading = false;
            }
        },

        loadFallbackSurahs() {
            // Fallback surahs data (first 10 surahs)
            this.surahs = [
                { number: 1, name: 'الفاتحة', englishName: 'Al-Fatihah', numberOfAyahs: 7, revelationType: 'Meccan' },
                { number: 2, name: 'البقرة', englishName: 'Al-Baqarah', numberOfAyahs: 286, revelationType: 'Medinan' },
                { number: 3, name: 'آل عمران', englishName: 'Ali Imran', numberOfAyahs: 200, revelationType: 'Medinan' },
                { number: 4, name: 'النساء', englishName: 'An-Nisa', numberOfAyahs: 176, revelationType: 'Medinan' },
                { number: 5, name: 'المائدة', englishName: 'Al-Ma\'idah', numberOfAyahs: 120, revelationType: 'Medinan' },
                { number: 6, name: 'الأنعام', englishName: 'Al-An\'am', numberOfAyahs: 165, revelationType: 'Meccan' },
                { number: 7, name: 'الأعراف', englishName: 'Al-A\'raf', numberOfAyahs: 206, revelationType: 'Meccan' },
                { number: 8, name: 'الأنفال', englishName: 'Al-Anfal', numberOfAyahs: 75, revelationType: 'Medinan' },
                { number: 9, name: 'التوبة', englishName: 'At-Tawbah', numberOfAyahs: 129, revelationType: 'Medinan' },
                { number: 10, name: 'يونس', englishName: 'Yunus', numberOfAyahs: 109, revelationType: 'Meccan' }
            ];
        },

        getEnglishSurahName(surahId) {
            const englishNames = {
                1: 'Al-Fatihah', 2: 'Al-Baqarah', 3: 'Ali \'Imran', 4: 'An-Nisa', 5: 'Al-Ma\'idah',
                6: 'Al-An\'am', 7: 'Al-A\'raf', 8: 'Al-Anfal', 9: 'At-Tawbah', 10: 'Yunus',
                11: 'Hud', 12: 'Yusuf', 13: 'Ar-Ra\'d', 14: 'Ibrahim', 15: 'Al-Hijr',
                16: 'An-Nahl', 17: 'Al-Isra', 18: 'Al-Kahf', 19: 'Maryam', 20: 'Taha',
                21: 'Al-Anbya', 22: 'Al-Hajj', 23: 'Al-Mu\'minun', 24: 'An-Nur', 25: 'Al-Furqan',
                26: 'Ash-Shu\'ara', 27: 'An-Naml', 28: 'Al-Qasas', 29: 'Al-\'Ankabut', 30: 'Ar-Rum',
                31: 'Luqman', 32: 'As-Sajdah', 33: 'Al-Ahzab', 34: 'Saba', 35: 'Fatir',
                36: 'Ya-Sin', 37: 'As-Saffat', 38: 'Sad', 39: 'Az-Zumar', 40: 'Ghafir',
                41: 'Fussilat', 42: 'Ash-Shuraa', 43: 'Az-Zukhruf', 44: 'Ad-Dukhan', 45: 'Al-Jathiyah',
                46: 'Al-Ahqaf', 47: 'Muhammad', 48: 'Al-Fath', 49: 'Al-Hujurat', 50: 'Qaf',
                51: 'Adh-Dhariyat', 52: 'At-Tur', 53: 'An-Najm', 54: 'Al-Qamar', 55: 'Ar-Rahman',
                56: 'Al-Waqi\'ah', 57: 'Al-Hadid', 58: 'Al-Mujadila', 59: 'Al-Hashr', 60: 'Al-Mumtahanah',
                61: 'As-Saff', 62: 'Al-Jumu\'ah', 63: 'Al-Munafiqun', 64: 'At-Taghabun', 65: 'At-Talaq',
                66: 'At-Tahrim', 67: 'Al-Mulk', 68: 'Al-Qalam', 69: 'Al-Haqqah', 70: 'Al-Ma\'arij',
                71: 'Nuh', 72: 'Al-Jinn', 73: 'Al-Muzzammil', 74: 'Al-Muddaththir', 75: 'Al-Qiyamah',
                76: 'Al-Insan', 77: 'Al-Mursalat', 78: 'An-Naba', 79: 'An-Nazi\'at', 80: 'Abasa',
                81: 'At-Takwir', 82: 'Al-Infitar', 83: 'Al-Mutaffifin', 84: 'Al-Inshiqaq', 85: 'Al-Buruj',
                86: 'At-Tariq', 87: 'Al-A\'la', 88: 'Al-Ghashiyah', 89: 'Al-Fajr', 90: 'Al-Balad',
                91: 'Ash-Shams', 92: 'Al-Layl', 93: 'Ad-Duhaa', 94: 'Ash-Sharh', 95: 'At-Tin',
                96: 'Al-\'Alaq', 97: 'Al-Qadr', 98: 'Al-Bayyinah', 99: 'Az-Zalzalah', 100: 'Al-\'Adiyat',
                101: 'Al-Qari\'ah', 102: 'At-Takathur', 103: 'Al-\'Asr', 104: 'Al-Humazah', 105: 'Al-Fil',
                106: 'Quraysh', 107: 'Al-Ma\'un', 108: 'Al-Kawthar', 109: 'Al-Kafirun', 110: 'An-Nasr',
                111: 'Al-Masad', 112: 'Al-Ikhlas', 113: 'Al-Falaq', 114: 'An-Nas'
            };
            return englishNames[surahId] || `Surah ${surahId}`;
        },

        getAyahCount(surahId) {
            const ayahCounts = {
                1: 7, 2: 286, 3: 200, 4: 176, 5: 120, 6: 165, 7: 206, 8: 75, 9: 129, 10: 109,
                11: 123, 12: 111, 13: 43, 14: 52, 15: 99, 16: 128, 17: 111, 18: 110, 19: 98, 20: 135,
                21: 112, 22: 78, 23: 118, 24: 64, 25: 77, 26: 227, 27: 93, 28: 88, 29: 69, 30: 60,
                31: 34, 32: 30, 33: 73, 34: 54, 35: 45, 36: 83, 37: 182, 38: 88, 39: 75, 40: 85,
                41: 54, 42: 53, 43: 89, 44: 59, 45: 37, 46: 35, 47: 38, 48: 29, 49: 18, 50: 45,
                51: 60, 52: 49, 53: 62, 54: 55, 55: 78, 56: 96, 57: 29, 58: 22, 59: 24, 60: 13,
                61: 14, 62: 11, 63: 11, 64: 18, 65: 12, 66: 12, 67: 30, 68: 52, 69: 52, 70: 44,
                71: 28, 72: 28, 73: 20, 74: 56, 75: 40, 76: 31, 77: 50, 78: 40, 79: 46, 80: 42,
                81: 29, 82: 19, 83: 36, 84: 25, 85: 22, 86: 17, 87: 19, 88: 26, 89: 30, 90: 20,
                91: 15, 92: 21, 93: 11, 94: 8, 95: 8, 96: 19, 97: 5, 98: 8, 99: 8, 100: 11,
                101: 11, 102: 8, 103: 3, 104: 9, 105: 5, 106: 4, 107: 7, 108: 3, 109: 6, 110: 3,
                111: 5, 112: 4, 113: 5, 114: 6
            };
            return ayahCounts[surahId] || 0;
        },

        async playAudio(surah) {
            try {
                this.loadingAudio = surah.number;

                // Get reciter data to find the correct moshaf and server
                const reciterResponse = await fetch(`https://mp3quran.net/api/v3/reciters?language=ar&reciter=${this.reciterId}`);
                const reciterData = await reciterResponse.json();

                let audioUrl = '';

                if (reciterData.reciters && reciterData.reciters.length > 0) {
                    const reciter = reciterData.reciters[0];
                    // Find the main moshaf (usually the first one with type 11 - Hafs)
                    const mainMoshaf = reciter.moshaf.find(m => m.moshaf_type === 11) || reciter.moshaf[0];

                    if (mainMoshaf) {
                        // Check if this surah is available in the moshaf
                        const surahList = mainMoshaf.surah_list.split(',').map(s => parseInt(s));
                        if (surahList.includes(surah.number)) {
                            // Construct audio URL using mp3quran.net structure
                            const paddedSurahNumber = surah.number.toString().padStart(3, '0');
                            audioUrl = `${mainMoshaf.server}${paddedSurahNumber}.mp3`;
                        } else {
                            throw new Error('هذه السورة غير متاحة لهذا القارئ');
                        }
                    }
                }

                if (!audioUrl) {
                    throw new Error('لم يتم العثور على ملف الصوت');
                }

                // Stop current audio if playing
                this.stopAudio();

                // Set new audio
                this.$refs.audioPlayer.src = audioUrl;

                // Play audio
                await this.$refs.audioPlayer.play();

                // Find current surah index
                const currentIndex = this.surahs.findIndex(s => s.number === surah.number);

                this.currentAudio = {
                    playing: true,
                    isPlaying: true,
                    surahName: surah.englishName,
                    surahArabicName: surah.name,
                    surahNumber: surah.number,
                    progress: 0,
                    currentTime: 0,
                    duration: 0,
                    volume: this.currentAudio.volume || 75,
                    muted: this.currentAudio.muted || false,
                    currentSurahIndex: currentIndex
                };

                // Set volume
                this.$refs.audioPlayer.volume = this.currentAudio.volume / 100;

                this.loadingAudio = null;
            } catch (error) {
                console.error('Error playing audio:', error);
                alert(error.message || 'خطأ في تشغيل الصوت. يرجى المحاولة مرة أخرى.');
                this.loadingAudio = null;
            }
        },

        togglePlay() {
            if (this.currentAudio.isPlaying) {
                this.$refs.audioPlayer.pause();
                this.currentAudio.isPlaying = false;
            } else {
                this.$refs.audioPlayer.play();
                this.currentAudio.isPlaying = true;
            }
        },

        stopAudio() {
            this.$refs.audioPlayer.pause();
            this.$refs.audioPlayer.currentTime = 0;
            this.currentAudio = {
                playing: false,
                isPlaying: false,
                surahName: '',
                surahArabicName: '',
                surahNumber: 0,
                progress: 0,
                currentTime: 0,
                duration: 0,
                volume: this.currentAudio.volume || 75,
                muted: this.currentAudio.muted || false,
                currentSurahIndex: -1
            };
        },

        async downloadAudio(surah) {
            try {
                this.downloadingAudio = surah.number;

                // Get reciter data to find the correct moshaf and server
                const reciterResponse = await fetch(`https://mp3quran.net/api/v3/reciters?language=ar&reciter=${this.reciterId}`);
                const reciterData = await reciterResponse.json();

                let audioUrl = '';

                if (reciterData.reciters && reciterData.reciters.length > 0) {
                    const reciter = reciterData.reciters[0];
                    // Find the main moshaf (usually the first one with type 11 - Hafs)
                    const mainMoshaf = reciter.moshaf.find(m => m.moshaf_type === 11) || reciter.moshaf[0];

                    if (mainMoshaf) {
                        // Check if this surah is available in the moshaf
                        const surahList = mainMoshaf.surah_list.split(',').map(s => parseInt(s));
                        if (surahList.includes(surah.number)) {
                            // Construct audio URL using mp3quran.net structure
                            const paddedSurahNumber = surah.number.toString().padStart(3, '0');
                            audioUrl = `${mainMoshaf.server}${paddedSurahNumber}.mp3`;
                        }
                    }
                }

                if (audioUrl) {
                    // Show success message
                    this.showNotification('بدء التحميل...', 'success');

                    const link = document.createElement('a');
                    link.href = audioUrl;
                    link.download = `${surah.name} - ${this.reciterArabicName}.mp3`;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);

                    // Show completion message after a delay
                    setTimeout(() => {
                        this.showNotification('تم بدء التحميل بنجاح', 'success');
                    }, 1000);
                } else {
                    this.showNotification('هذه السورة غير متاحة للتحميل من هذا القارئ', 'error');
                }

                this.downloadingAudio = null;
            } catch (error) {
                console.error('Error downloading audio:', error);
                this.showNotification('خطأ في تحميل الملف. يرجى المحاولة مرة أخرى.', 'error');
                this.downloadingAudio = null;
            }
        },

        showSurahInfo(surah) {
            this.selectedSurah = surah;
            this.showingInfo = true;
        },

        closeSurahInfo() {
            this.showingInfo = false;
            this.selectedSurah = null;
        },

        showNotification(message, type = 'info') {
            // Create notification element
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg transition-all duration-300 transform translate-x-full`;

            if (type === 'success') {
                notification.className += ' bg-green-500 text-white';
            } else if (type === 'error') {
                notification.className += ' bg-red-500 text-white';
            } else {
                notification.className += ' bg-blue-500 text-white';
            }

            notification.innerHTML = `
                <div class="flex items-center">
                    <i class="fas ${type === 'success' ? 'fa-check-circle' : type === 'error' ? 'fa-exclamation-circle' : 'fa-info-circle'} ml-2"></i>
                    <span>${message}</span>
                </div>
            `;

            document.body.appendChild(notification);

            // Animate in
            setTimeout(() => {
                notification.classList.remove('translate-x-full');
            }, 100);

            // Remove after 3 seconds
            setTimeout(() => {
                notification.classList.add('translate-x-full');
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 3000);
        },

        updateProgress() {
            if (this.$refs.audioPlayer.duration) {
                this.currentAudio.progress = (this.$refs.audioPlayer.currentTime / this.$refs.audioPlayer.duration) * 100;
                this.currentAudio.currentTime = this.$refs.audioPlayer.currentTime;
                this.currentAudio.duration = this.$refs.audioPlayer.duration;

                // Update CSS custom property for visual feedback
                this.updateProgressSliderStyle();
            }
        },

        onAudioLoaded() {
            this.currentAudio.duration = this.$refs.audioPlayer.duration;
        },

        onAudioEnded() {
            // Auto-play next surah
            this.nextSurah();
        },

        seekAudio() {
            if (this.$refs.audioPlayer.duration) {
                const seekTime = (this.currentAudio.progress / 100) * this.$refs.audioPlayer.duration;
                this.$refs.audioPlayer.currentTime = seekTime;
            }
        },

        updateVolume() {
            this.$refs.audioPlayer.volume = this.currentAudio.volume / 100;
            this.currentAudio.muted = this.currentAudio.volume === 0;

            // Update CSS custom property for visual feedback
            this.updateVolumeSliderStyle();
        },

        updateVolumeSliderStyle() {
            const volumeSlider = document.querySelector('.volume-range');
            if (volumeSlider) {
                volumeSlider.style.setProperty('--volume', `${this.currentAudio.volume}%`);
            }
        },

        updateProgressSliderStyle() {
            const progressSlider = document.querySelector('.progress-range');
            if (progressSlider) {
                progressSlider.style.setProperty('--progress', `${this.currentAudio.progress}%`);
            }
        },

        toggleMute() {
            if (this.currentAudio.muted) {
                this.currentAudio.volume = 75;
                this.currentAudio.muted = false;
            } else {
                this.currentAudio.volume = 0;
                this.currentAudio.muted = true;
            }
            this.updateVolume();
        },

        previousSurah() {
            if (this.currentAudio.currentSurahIndex > 0) {
                const previousSurah = this.surahs[this.currentAudio.currentSurahIndex - 1];
                this.playAudio(previousSurah);
            } else {
                // Go to last surah if at beginning
                const lastSurah = this.surahs[this.surahs.length - 1];
                this.playAudio(lastSurah);
            }
        },

        nextSurah() {
            if (this.currentAudio.currentSurahIndex < this.surahs.length - 1) {
                const nextSurah = this.surahs[this.currentAudio.currentSurahIndex + 1];
                this.playAudio(nextSurah);
            } else {
                // Stop if at end
                this.stopAudio();
            }
        },

        formatTime(seconds) {
            if (!seconds || isNaN(seconds)) return '0:00';
            const minutes = Math.floor(seconds / 60);
            const remainingSeconds = Math.floor(seconds % 60);
            return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
        },

        goBack() {
            window.location.href = 'index.html';
        },

        filterSurahs() {
            if (!this.searchQuery.trim()) {
                this.filteredSurahs = [...this.surahs];
                return;
            }

            const query = this.normalizeArabicText(this.searchQuery.toLowerCase());

            this.filteredSurahs = this.surahs.filter(surah => {
                const arabicName = this.normalizeArabicText(surah.name.toLowerCase());
                const englishName = surah.englishName.toLowerCase();
                const surahNumber = surah.number.toString();
                const revelationType = surah.revelationType === 'Meccan' ? 'مكية' : 'مدنية';
                const revelationTypeNormalized = this.normalizeArabicText(revelationType.toLowerCase());

                return arabicName.includes(query) ||
                    englishName.includes(query) ||
                    surahNumber.includes(query) ||
                    revelationTypeNormalized.includes(query);
            });
        },

        normalizeArabicText(text) {
            // Normalize Arabic text by removing diacritics and standardizing characters
            return text
                .replace(/[ًٌٍَُِّْ]/g, '') // Remove diacritics
                .replace(/[أإآ]/g, 'ا')      // Normalize alif
                .replace(/[ةه]/g, 'ه')       // Normalize teh marbuta
                .replace(/[ىي]/g, 'ي')       // Normalize yeh
                .trim();
        },

        clearSearch() {
            this.searchQuery = '';
            this.filteredSurahs = [...this.surahs];
        }
    }
}
