<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>قارئ القرآن - السور</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Cairo:wght@400;600;700&display=swap"
        rel="stylesheet">
    <link rel="stylesheet" href="styles/main.css">
</head>

<body class="reciter-page">
    <div x-data="reciterApp()" class="min-h-screen">
        <!-- Modern Header -->
        <header class="relative overflow-hidden">
            <!-- Background Pattern -->
            <div class="absolute inset-0 gradient-light opacity-90"></div>
            <div class="absolute inset-0"
                style="background-image: radial-gradient(circle at 25% 25%, rgba(5, 150, 105, 0.1) 0%, transparent 50%), radial-gradient(circle at 75% 75%, rgba(2, 132, 199, 0.1) 0%, transparent 50%);">
            </div>

            <div class="relative container mx-auto px-6 py-12">
                <!-- Back Button -->
                <button @click="goBack()"
                    class="mb-8 inline-flex items-center px-4 py-2 bg-white/80 backdrop-blur-sm rounded-full shadow-sm border border-gray-200 text-green-600 hover:text-green-800 hover:bg-white transition-all duration-200 animate-fade-in-up">
                    <i class="fas fa-arrow-right ml-2"></i>
                    العودة إلى القراء
                </button>

                <!-- Enhanced Reciter Info -->
                <div class="modern-card rounded-3xl p-8 animate-fade-in-up" style="animation-delay: 0.2s;">
                    <div class="flex flex-col md:flex-row items-center md:items-start text-center md:text-right">
                        <!-- Reciter Avatar -->
                        <div class="relative mb-6 md:mb-0 md:ml-8">
                            <div
                                class="w-32 h-32 rounded-full gradient-primary flex items-center justify-center shadow-xl">
                                <i class="fas fa-user text-white text-4xl"></i>
                            </div>
                            <!-- Decorative ring -->
                            <div
                                class="absolute inset-0 w-32 h-32 rounded-full border-4 border-green-200 opacity-50 animate-pulse">
                            </div>
                        </div>

                        <!-- Reciter Details -->
                        <div class="flex-1 space-y-4">
                            <div>
                                <h1 class="heading-primary arabic-text mb-2" x-text="reciterArabicName"></h1>
                                <p class="text-xl text-gray-600 font-medium" x-text="reciterName"></p>
                                <p class="text-lg text-gray-500" x-text="reciterCountry"></p>
                            </div>

                            <!-- Stats -->
                            <div class="flex flex-wrap justify-center md:justify-start gap-4 mt-6">
                                <div
                                    class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-green-50 to-blue-50 rounded-full border border-green-100">
                                    <i class="fas fa-book-quran text-green-600 ml-2"></i>
                                    <span class="text-sm font-semibold text-gray-700">١١٤ سورة متاحة</span>
                                </div>
                                <div
                                    class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-blue-50 to-purple-50 rounded-full border border-blue-100">
                                    <i class="fas fa-headphones text-blue-600 ml-2"></i>
                                    <span class="text-sm font-semibold text-gray-700">جودة عالية</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </header>



        <!-- Main Content Area -->
        <main class="container mx-auto px-6 pb-16">
            <!-- Modern Search Section -->
            <div x-show="!loading" class="mb-12 animate-fade-in-up" style="animation-delay: 0.4s;">
                <div class="max-w-lg mx-auto">
                    <div class="relative group">
                        <div class="absolute inset-y-0 right-0 pr-4 flex items-center pointer-events-none">
                            <i
                                class="fas fa-search text-gray-400 group-focus-within:text-green-600 transition-colors duration-300"></i>
                        </div>
                        <input type="text" x-model="searchQuery" @input.debounce.300ms="filterSurahs()"
                            placeholder="ابحث في السور... (مثال: الفاتحة، مكية، 1)"
                            class="search-input w-full pl-12 pr-12 py-4 rounded-2xl text-right arabic-text text-lg focus:outline-none"
                            dir="rtl">
                        <button x-show="searchQuery.length > 0" @click="clearSearch()"
                            x-transition:enter="transition ease-out duration-200"
                            x-transition:enter-start="opacity-0 scale-95" x-transition:enter-end="opacity-100 scale-100"
                            x-transition:leave="transition ease-in duration-150"
                            x-transition:leave-start="opacity-100 scale-100" x-transition:leave-end="opacity-0 scale-95"
                            class="absolute inset-y-0 left-0 pl-4 flex items-center cursor-pointer text-gray-400 hover:text-red-500 transition-colors duration-200">
                            <i class="fas fa-times-circle text-lg"></i>
                        </button>
                    </div>

                    <!-- Enhanced Search Results Count -->
                    <div x-show="searchQuery.length > 0" x-transition:enter="transition ease-out duration-300"
                        x-transition:enter-start="opacity-0 transform translate-y-2"
                        x-transition:enter-end="opacity-100 transform translate-y-0" class="mt-4 text-center">
                        <div
                            class="inline-flex items-center px-4 py-2 bg-white/80 backdrop-blur-sm rounded-full shadow-sm border border-gray-200">
                            <i class="fas fa-filter text-green-600 ml-2"></i>
                            <span class="text-sm font-medium text-gray-700">
                                <span x-text="filteredSurahs.length" class="text-gradient font-bold"></span>
                                سورة من أصل
                                <span x-text="surahs.length" class="font-bold"></span>
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Modern Loading State -->
            <div x-show="loading" class="flex flex-col justify-center items-center py-24">
                <div class="modern-spinner mb-6"></div>
                <p class="text-lg font-medium text-gray-600 arabic-text">جاري تحميل السور...</p>
                <p class="text-sm text-gray-500 mt-2">يرجى الانتظار قليلاً</p>
            </div>

            <!-- Enhanced No Results Message -->
            <div x-show="!loading && filteredSurahs.length === 0 && searchQuery.length > 0"
                class="text-center py-20 animate-fade-in-up">
                <div class="max-w-lg mx-auto modern-card rounded-3xl p-12">
                    <div class="w-24 h-24 mx-auto mb-6 rounded-full gradient-light flex items-center justify-center">
                        <i class="fas fa-search text-gray-400 text-3xl"></i>
                    </div>
                    <h3 class="text-3xl font-bold text-gray-700 mb-4 arabic-text">لا توجد نتائج</h3>
                    <p class="text-gray-600 mb-8 text-lg leading-relaxed">
                        لم نجد أي سورة تطابق بحثك عن
                        <span x-text="searchQuery" class="font-bold text-gradient"></span>
                    </p>
                    <button @click="clearSearch()"
                        class="btn-modern text-white py-3 px-8 rounded-2xl font-semibold text-lg">
                        <i class="fas fa-refresh ml-2"></i>
                        مسح البحث والعودة
                    </button>
                </div>
            </div>



            <!-- Modern Surahs List -->
            <div x-show="!loading && filteredSurahs.length > 0" x-cloak class="space-y-6"
                :class="currentAudio.playing ? 'pb-32' : 'pb-8'">
                <template x-for="(surah, index) in filteredSurahs" :key="surah.number">
                    <div class="modern-card rounded-3xl overflow-hidden group" :class="'animate-fade-in-up'"
                        :style="`animation-delay: ${index * 0.1}s`">
                        <!-- Modern Surah Header -->
                        <div class="gradient-light px-8 py-6 border-b border-gray-100/50">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <!-- Enhanced Surah Number Badge -->
                                    <div class="relative">
                                        <div
                                            class="w-16 h-16 gradient-primary rounded-2xl flex items-center justify-center shadow-xl ml-6 group-hover:scale-110 transition-transform duration-300">
                                            <span class="text-white font-bold text-xl" x-text="surah.number"></span>
                                        </div>
                                        <!-- Enhanced Playing indicator -->
                                        <div x-show="currentAudio.playing && currentAudio.surahName === surah.englishName"
                                            class="absolute -top-2 -right-2 w-6 h-6 gradient-accent rounded-full animate-pulse shadow-lg">
                                            <div
                                                class="w-full h-full rounded-full border-2 border-white flex items-center justify-center">
                                                <i class="fas fa-volume-up text-white text-xs"></i>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Enhanced Surah Names -->
                                    <div class="flex-1">
                                        <h3 class="text-2xl font-bold mb-2 arabic-text leading-tight"
                                            :class="currentAudio.playing && currentAudio.surahName === surah.englishName ? 'text-gradient' : 'text-gray-800 group-hover:text-gradient'"
                                            x-text="surah.name">
                                        </h3>
                                        <p class="text-gray-600 font-medium text-lg" x-text="surah.englishName"></p>
                                    </div>
                                </div>

                                <!-- Enhanced Surah Stats -->
                                <div class="text-left space-y-3">
                                    <div
                                        class="inline-flex items-center px-3 py-2 bg-white/80 backdrop-blur-sm rounded-full shadow-sm">
                                        <i class="fas fa-book-open ml-2 text-green-600"></i>
                                        <span class="text-sm font-semibold text-gray-700">
                                            <span x-text="surah.numberOfAyahs"></span> آية
                                        </span>
                                    </div>
                                    <div class="inline-flex items-center px-3 py-2 rounded-full text-sm font-medium shadow-sm"
                                        :class="surah.revelationType === 'Meccan' ? 'bg-amber-100 text-amber-800 border border-amber-200' : 'bg-emerald-100 text-emerald-800 border border-emerald-200'">
                                        <i class="fas fa-map-marker-alt ml-2"></i>
                                        <span x-text="surah.revelationType === 'Meccan' ? 'مكية' : 'مدنية'"></span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="px-6 py-4 bg-gray-50">
                            <div class="flex items-center justify-center space-x-reverse space-x-4">
                                <!-- Play/Pause Button -->
                                <button @click="playAudio(surah)" :disabled="loadingAudio === surah.number"
                                    class="flex items-center px-6 py-3 rounded-xl font-medium transition-all duration-200 transform hover:scale-105 shadow-md"
                                    :class="loadingAudio === surah.number ?
                                        'bg-gray-400 text-white cursor-not-allowed' :
                                        'bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white shadow-green-200'">
                                    <i x-show="loadingAudio !== surah.number" class="fas fa-play ml-2"></i>
                                    <i x-show="loadingAudio === surah.number" class="fas fa-spinner fa-spin ml-2"></i>
                                    <span x-text="loadingAudio === surah.number ? 'جاري التحميل...' : 'تشغيل'"></span>
                                </button>

                                <!-- Download Button -->
                                <button @click="downloadAudio(surah)" :disabled="downloadingAudio === surah.number"
                                    class="flex items-center px-6 py-3 rounded-xl font-medium transition-all duration-200 transform hover:scale-105 shadow-md shadow-blue-200"
                                    :class="downloadingAudio === surah.number ?
                                        'bg-gray-400 text-white cursor-not-allowed' :
                                        'bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white'">
                                    <i x-show="downloadingAudio !== surah.number" class="fas fa-download ml-2"></i>
                                    <i x-show="downloadingAudio === surah.number"
                                        class="fas fa-spinner fa-spin ml-2"></i>
                                    <span
                                        x-text="downloadingAudio === surah.number ? 'جاري التحميل...' : 'تحميل'"></span>
                                </button>

                                <!-- Info Button -->
                                <button @click="showSurahInfo(surah)"
                                    class="flex items-center px-4 py-3 bg-gradient-to-r from-amber-500 to-orange-600 hover:from-amber-600 hover:to-orange-700 text-white rounded-xl font-medium transition-all duration-200 transform hover:scale-105 shadow-md shadow-amber-200">
                                    <i class="fas fa-info-circle"></i>
                                </button>
                            </div>

                            <!-- Progress Bar for Currently Playing -->
                            <div x-show="currentAudio.playing && currentAudio.surahName === surah.englishName"
                                class="mt-4 bg-gray-200 rounded-full h-2 overflow-hidden">
                                <div class="bg-gradient-to-r from-green-400 to-green-500 h-full rounded-full transition-all duration-300"
                                    :style="`width: ${currentAudio.progress}%`"></div>
                            </div>
                        </div>
                    </div>
                </template>
            </div>

            <!-- Audio Element -->
            <audio x-ref="audioPlayer" @ended="onAudioEnded()" @timeupdate="updateProgress()"
                @loadedmetadata="onAudioLoaded()"></audio>

            <!-- Enhanced Bottom Audio Player -->
            <div x-show="currentAudio.playing" x-transition:enter="transition ease-out duration-500"
                x-transition:enter-start="transform translate-y-full opacity-0"
                x-transition:enter-end="transform translate-y-0 opacity-100"
                x-transition:leave="transition ease-in duration-300"
                x-transition:leave-start="transform translate-y-0 opacity-100"
                x-transition:leave-end="transform translate-y-full opacity-0"
                class="fixed bottom-0 left-0 right-0 z-50 audio-player shadow-2xl">

                <!-- Mini Waveform Visualization -->
                <div class="audio-waveform h-1"></div>

                <!-- Main Player Content -->
                <div class="container mx-auto px-4 py-4">
                    <div class="flex items-center justify-between">

                        <!-- Left Section: Surah Info -->
                        <div class="flex items-center flex-1 min-w-0 mobile-compact">
                            <!-- Album Art / Surah Number -->
                            <div class="relative flex-shrink-0 ml-2 md:ml-4">
                                <div
                                    class="w-12 h-12 md:w-16 md:h-16 bg-gradient-to-br from-green-500 to-blue-500 rounded-xl flex items-center justify-center shadow-lg">
                                    <span class="text-white font-bold text-sm md:text-lg"
                                        x-text="currentAudio.surahNumber"></span>
                                </div>
                                <!-- Playing Animation -->
                                <div
                                    class="absolute -top-1 -right-1 w-5 h-5 bg-gradient-to-r from-red-500 to-pink-500 rounded-full">
                                    <div
                                        class="w-full h-full bg-gradient-to-r from-red-400 to-pink-400 rounded-full pulse-ring">
                                    </div>
                                    <div
                                        class="absolute inset-1 bg-white rounded-full flex items-center justify-center">
                                        <div class="w-2 h-2 bg-red-500 rounded-full audio-bounce"></div>
                                    </div>
                                </div>
                            </div>

                            <!-- Surah Details -->
                            <div class="flex-1 min-w-0 mr-2 md:mr-4">
                                <h3 class="text-sm md:text-lg font-bold text-gray-800 arabic-text truncate"
                                    x-text="currentAudio.surahArabicName"></h3>
                                <p class="text-xs md:text-sm text-gray-600 truncate" x-text="currentAudio.surahName">
                                </p>
                                <p class="text-xs text-gray-500 arabic-text truncate hidden md:block"
                                    x-text="reciterArabicName"></p>
                            </div>
                        </div>

                        <!-- Center Section: Controls -->
                        <div class="flex items-center space-x-reverse space-x-2 md:space-x-4 flex-shrink-0">
                            <!-- Previous Button -->
                            <button @click="previousSurah()"
                                class="p-2 md:p-3 text-gray-600 hover:text-green-600 transition-colors rounded-full hover:bg-green-50">
                                <i class="fas fa-step-backward text-sm md:text-base"></i>
                            </button>

                            <!-- Play/Pause Button -->
                            <button @click="togglePlay()"
                                class="w-10 h-10 md:w-12 md:h-12 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white rounded-full flex items-center justify-center shadow-lg transform hover:scale-105 transition-all duration-200">
                                <i :class="currentAudio.isPlaying ? 'fas fa-pause' : 'fas fa-play'"
                                    class="text-sm md:text-lg"></i>
                            </button>

                            <!-- Next Button -->
                            <button @click="nextSurah()"
                                class="p-2 md:p-3 text-gray-600 hover:text-green-600 transition-colors rounded-full hover:bg-green-50">
                                <i class="fas fa-step-forward text-sm md:text-base"></i>
                            </button>
                        </div>

                        <!-- Right Section: Volume & Close -->
                        <div class="flex items-center space-x-reverse space-x-3 flex-shrink-0">
                            <!-- Volume Control -->
                            <div class="hidden md:flex items-center space-x-reverse space-x-2">
                                <button @click="toggleMute()"
                                    class="p-2 text-gray-600 hover:text-blue-600 transition-colors">
                                    <i
                                        :class="currentAudio.muted ? 'fas fa-volume-mute' : currentAudio.volume > 50 ? 'fas fa-volume-up' : 'fas fa-volume-down'"></i>
                                </button>
                                <input type="range" x-model="currentAudio.volume" @input="updateVolume()" min="0"
                                    max="100" class="w-20 custom-range volume-range"
                                    :style="`--volume: ${currentAudio.volume}%`">
                            </div>

                            <!-- Close Button -->
                            <button @click="stopAudio()"
                                class="p-2 text-gray-500 hover:text-red-500 transition-colors rounded-full hover:bg-red-50">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Progress Bar Section -->
                    <div class="mt-4 flex items-center space-x-reverse space-x-3">
                        <!-- Current Time -->
                        <span class="text-xs text-gray-500 w-12 text-center"
                            x-text="formatTime(currentAudio.currentTime)"></span>

                        <!-- Progress Bar -->
                        <div class="flex-1 relative">
                            <input type="range" x-model="currentAudio.progress" @input="seekAudio()" min="0" max="100"
                                class="w-full custom-range progress-range"
                                :style="`--progress: ${currentAudio.progress}%`">
                        </div>

                        <!-- Total Duration -->
                        <span class="text-xs text-gray-500 w-12 text-center"
                            x-text="formatTime(currentAudio.duration)"></span>
                    </div>
                </div>
            </div>

            <!-- Surah Info Modal -->
            <div x-show="showingInfo" x-transition:enter="transition ease-out duration-300"
                x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100"
                x-transition:leave="transition ease-in duration-200" x-transition:leave-start="opacity-100"
                x-transition:leave-end="opacity-0"
                class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4"
                @click="closeSurahInfo()">

                <div x-show="showingInfo" x-transition:enter="transition ease-out duration-300"
                    x-transition:enter-start="opacity-0 transform scale-95"
                    x-transition:enter-end="opacity-100 transform scale-100"
                    x-transition:leave="transition ease-in duration-200"
                    x-transition:leave-start="opacity-100 transform scale-100"
                    x-transition:leave-end="opacity-0 transform scale-95"
                    class="bg-white rounded-2xl shadow-2xl max-w-md w-full max-h-96 overflow-y-auto" @click.stop>

                    <!-- Modal Header -->
                    <div class="bg-gradient-to-r from-green-500 to-blue-500 text-white p-6 rounded-t-2xl">
                        <div class="flex items-center justify-between">
                            <h3 class="text-xl font-bold arabic-text" x-text="selectedSurah?.name"></h3>
                            <button @click="closeSurahInfo()" class="text-white hover:text-gray-200 transition-colors">
                                <i class="fas fa-times text-xl"></i>
                            </button>
                        </div>
                        <p class="text-green-100 mt-1" x-text="selectedSurah?.englishName"></p>
                    </div>

                    <!-- Modal Content -->
                    <div class="p-6">
                        <div class="space-y-4">
                            <!-- Surah Number -->
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <span class="text-gray-600">رقم السورة:</span>
                                <span class="font-bold text-green-600" x-text="selectedSurah?.number"></span>
                            </div>

                            <!-- Number of Ayahs -->
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <span class="text-gray-600">عدد الآيات:</span>
                                <span class="font-bold text-blue-600" x-text="selectedSurah?.numberOfAyahs"></span>
                            </div>

                            <!-- Revelation Type -->
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <span class="text-gray-600">نوع السورة:</span>
                                <span class="px-3 py-1 rounded-full text-sm font-medium"
                                    :class="selectedSurah?.revelationType === 'Meccan' ? 'bg-amber-100 text-amber-800' : 'bg-emerald-100 text-emerald-800'"
                                    x-text="selectedSurah?.revelationType === 'Meccan' ? 'مكية' : 'مدنية'"></span>
                            </div>

                            <!-- Action Buttons -->
                            <div class="flex space-x-reverse space-x-3 pt-4">
                                <button @click="playAudio(selectedSurah); closeSurahInfo()"
                                    class="flex-1 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white py-3 px-4 rounded-xl font-medium transition-all duration-200">
                                    <i class="fas fa-play ml-2"></i>
                                    تشغيل
                                </button>
                                <button @click="downloadAudio(selectedSurah); closeSurahInfo()"
                                    class="flex-1 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white py-3 px-4 rounded-xl font-medium transition-all duration-200">
                                    <i class="fas fa-download ml-2"></i>
                                    تحميل
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
    </div>

    <script src="js/reciter.js"></script>


</body>

</html>